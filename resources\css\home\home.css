* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body,
html {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
}
.flip {
    animation: flip 0.5s ease-in-out;
}

@keyframes flip {
    0% {
        transform: rotateX(0deg);
    }
    50% {
        transform: rotateX(-90deg);
    }
    100% {
        transform: rotateX(0deg);
    }
}

.parent {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-auto-rows: min-content;
    gap: 8px;
}

.div2 {
    grid-column: span 5 / span 5;
    grid-row: span 2 / span 2;
    grid-row-start: 2;
}

.div3 {
    grid-column: span 2 / span 2;
    grid-row-start: 4;
}

.div4 {
    grid-column: span 3 / span 3;
    grid-column-start: 3;
    grid-row-start: 4;
}

.div5 {
    grid-column: span 5 / span 5;
    grid-row-start: 5;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    justify-content: space-between;
    padding: clamp(0.5rem, 2vw, 1.5rem);
    max-width: 100%;
    overflow-x: hidden;
}

.top-section {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.left-info {
    max-width: 50%;
}

.left-info .weather {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}
#iconcuaca {
    width: auto;
    height: auto;
    display: block;
}

.left-info .weather img {
    height: 50px;
    margin-right: 0.5rem;
}
.ketderajat span {
    font-size: 1.2rem;
}

.ketderajat {
    display: flex;
    flex-direction: column;
    justify-content: end;
    align-items: flex-start;
    font-size: 0.9rem;
    line-height: 1.2;
}

.time {
    padding: 0;
    margin: 0;
    font-size: clamp(3rem, 8vw, 5rem);
    color: #5b2991;
    line-height: 1;
    display: flex;
    align-items: stretch;
}

.date {
    line-height: 1;
    font-size: clamp(1rem, 3vw, 1.5rem);
    margin-top: 0.3rem;
    color: #5b2991;
}

/* Enhanced responsive typography scaling */
@media (max-width: 768px) {
    .time { font-size: clamp(2.5rem, 10vw, 3.5rem); }
    .date { font-size: clamp(0.9rem, 4vw, 1.2rem); }
}

@media (min-width: 1025px) and (max-width: 1366px) {
    .time { font-size: clamp(4rem, 6vw, 5.5rem); }
    .date { font-size: clamp(1.3rem, 2.5vw, 1.7rem); }
}

@media (min-width: 1367px) {
    .time { font-size: clamp(5rem, 5vw, 6rem); }
    .date { font-size: clamp(1.5rem, 2vw, 2rem); }
}


.holiday {
    font-size: 1rem;
    font-style: italic;
    margin-bottom: 1rem;
}

/* TOP card - hanya bagian bawah terlihat */
.card-top {
    top: 0;
    opacity: 0.5;
    z-index: 1;
    transform: translateY(-25%); /* geser agar hanya bagian bawah muncul */
}

/* MIDDLE card - utama */
.card-middle {
    top: 50%;
    transform: translateY(-50%); /* posisikan benar-benar di tengah */
    opacity: 1;
    z-index: 3;
}

/* BOTTOM card - hanya bagian atas terlihat */
.card-bottom {
    bottom: 0;
    opacity: 0.5;
    z-index: 1;
    transform: translateY(25%); /* geser agar hanya bagian atas muncul */
}

@keyframes slide {
    0%,
    20% {
        transform: translateX(0);
    }
    33%,
    53% {
        transform: translateX(-100vw);
    }
    66%,
    86% {
        transform: translateX(-200vw);
    }
    100% {
        transform: translateX(0);
    }
}

.info-section {
    gap: 10px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.info-box {
    background: white;
    border-radius: clamp(8px, 2vw, 12px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.209);
    flex: 1;
    padding: clamp(0.5rem, 2vw, 1rem);
}

.info-box p {
    font-size: clamp(0.8rem, 2.5vw, 1.1rem);
}

.info-box ul {
    margin-top: clamp(0.3rem, 1vw, 0.7rem);
    font-size: clamp(0.7rem, 2vw, 1rem);
    padding-left: clamp(0.8rem, 2vw, 1.2rem);
}

.bottom-nav {
    display: flex;
    justify-content: space-around;
    border-radius: clamp(8px, 2vw, 15px);
    padding: clamp(0.5rem, 2vw, 1rem);
    gap: clamp(0.5rem, 2vw, 1rem);
}

.bottom-nav button {
    background: white;
    border: none;
    border-radius: clamp(8px, 2vw, 12px);
    padding: clamp(0.3rem, 1.5vw, 0.7rem);
    font-size: clamp(0.7rem, 2vw, 0.9rem);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 1;
    margin: 0 clamp(0.2rem, 1vw, 0.4rem);
}

/* Responsive layout adjustments */
@media (max-width: 768px) {
    .top-section,
    .info-section {
        flex-direction: column;
    }

    .left-info,
    .right-logo {
        max-width: 100%;
    }

    .mitra-cards {
        flex-direction: column;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .info-section {
        gap: clamp(8px, 2vw, 15px);
    }

    .mitra-cards-stack {
        height: clamp(90px, 12vw, 110px);
    }
}

@media (min-width: 1025px) and (max-width: 1366px) {
    .info-section {
        gap: clamp(10px, 1.5vw, 20px);
    }

    .mitra-cards-stack {
        height: clamp(100px, 10vw, 120px);
    }

    .slider-wrapper {
        height: clamp(250px, 20vw, 300px);
    }
}

@media (min-width: 1367px) {
    .info-section {
        gap: clamp(15px, 1vw, 25px);
    }

    .mitra-cards-stack {
        height: clamp(110px, 8vw, 140px);
    }

    .slider-wrapper {
        height: clamp(280px, 18vw, 350px);
    }
}

.btnmenu {
    background: white;
    border-radius: 25px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 1;
    margin: 0.5rem;
}

.mitra-cards-stack {
    position: relative;
    height: clamp(90px, 12vw, 120px); /* responsive height for card stacking */
    width: 100%;
}

.mitra-card {
    position: absolute;
    left: 0;
    width: 100%;
    background: white;
    border-radius: clamp(8px, 2vw, 15px);
    padding: clamp(0.3rem, 1.5vw, 0.7rem);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: clamp(0.5rem, 2vw, 1.2rem);
    transition: all 0.3s ease;
    font-size: clamp(0.7rem, 2.5vw, 1rem);
}

.mitra-card img {
    width: clamp(30px, 5vw, 50px);
    height: clamp(30px, 5vw, 50px);
    object-fit: contain;
}

/* Card atas (bagian bawahnya saja yang tampak) */
.card-top {
    top: 0;
    transform: translateY(-30%);
    opacity: 0.5;
    z-index: 1;
}

/* Card tengah (utama) */
.card-middle {
    margin-left: 20px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 1;
    z-index: 3;
}

/* Card bawah (bagian atasnya saja yang tampak) */
.card-bottom {
    bottom: 0;
    transform: translateY(30%);
    opacity: 0.5;
    z-index: 1;
}

/* animasi card */
.slider-wrapper {
    position: relative;
    width: 100%;
}

.slider-track {
    display: flex;
    width: 300%;
    transition: transform 0.5s ease-in-out;
}

.slide {
    width: 100%;
    flex-shrink: 0;
    border-radius: 10px;
}

.slider-dots {
    bottom: 10px;
}

.dot {
    height: 10px;
    width: 10px;
    margin: 0 5px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    border: none;
}

.dot.active,
.dot:hover {
    background-color: #717171;
}

/* image slide */
.slider-wrapper {
    height: clamp(200px, 25vw, 300px);
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.slider-track {
    display: flex;
    height: 100%;
    width: 100%;
}

.slide {
    width: 100%;
    height: 100%;
    flex-shrink: 0;
    border-radius: clamp(8px, 2vw, 12px);
    overflow: hidden;
    position: relative;
    border: 1px solid #ccc;
}

.slide-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    border-radius: clamp(8px, 2vw, 12px);
}
