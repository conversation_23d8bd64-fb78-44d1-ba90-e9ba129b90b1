document.addEventListener("DOMContentLoaded", function () {
    const stack = document.querySelector(".mitra-cards-stack");
    if (!stack) {
        console.warn("Element .mitra-cards-stack not found");
        return;
    }
    setInterval(() => {
        const topCard = stack.querySelector(".card-top");
        const middleCard = stack.querySelector(".card-middle");
        const bottomCard = stack.querySelector(".card-bottom");

        if (!topCard || !middleCard || !bottomCard) return;

        // Hapus class posisi
        topCard.classList.remove("card-top");
        middleCard.classList.remove("card-middle");
        bottomCard.classList.remove("card-bottom");

        // Putar posisi:
        // bottom → middle, middle → top, top → bottom
        topCard.classList.add("card-bottom");
        middleCard.classList.add("card-top");
        bottomCard.classList.add("card-middle");
    }, 3000); // setiap 3 detik
});
