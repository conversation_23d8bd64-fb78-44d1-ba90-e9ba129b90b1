<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Order - PT. Putera Wibowo Borneo</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <style>
        html {
            background: none;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background-image:
                linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 80%),
                url('<?php echo e(asset('images/bg.png')); ?>');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            overflow-x: hidden;
        }

        .page-container {
            flex: 1;
            padding: clamp(1rem, 3vw, 2rem);
        }

        .header {
            margin-bottom: 2rem;
        }

        .right-logo {
            text-align: center;
        }

        .logo-line {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 0.5rem;
        }

        .logo-line img {
            height: 60px;
            width: auto;
        }

        .slogan {
            font-size: 1.8rem;
            font-weight: bold;
            color: var(--text-primary);
            margin: 0;
        }

        .tagline {
            font-size: 1rem;
            color: var(--text-primary);
            font-style: italic;
        }

        .po-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .po-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .po-title {
            font-size: 2rem;
            font-weight: bold;
            color: var(--text-primary);
            margin: 0;
        }

        .search-container {
            margin-bottom: 1.5rem;
        }

        .search-input {
            width: 100%;
            max-width: 400px;
            padding: 0.75rem 1rem;
            border: 2px solid #e0e7ff;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--purple);
            box-shadow: 0 0 0 3px rgba(108, 86, 123, 0.1);
        }

        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .table {
            margin: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--purple) 0%, var(--dark-blue) 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 1rem 0.75rem;
            font-size: 0.9rem;
            text-align: center;
        }

        .table tbody td {
            padding: 1rem 0.75rem;
            vertical-align: middle;
            border-bottom: 1px solid #f0f0f0;
            font-size: 0.85rem;
        }

        .table tbody tr:hover {
            background-color: #f8f9ff;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-align: center;
            display: inline-block;
            min-width: 80px;
        }

        .status-ready {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .status-diajukan {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1.5rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .pagination-info {
            font-size: 0.9rem;
            color: var(--text-primary);
        }

        .pagination-controls {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .pagination-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            background: white;
            color: var(--text-primary);
            text-decoration: none;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .pagination-btn:hover {
            background: var(--purple);
            color: white;
            text-decoration: none;
        }

        .pagination-btn.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .home-button {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: linear-gradient(135deg, var(--purple) 0%, var(--dark-blue) 100%);
            color: white;
            padding: 1rem;
            border-radius: 50px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }

        .home-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            color: white;
            text-decoration: none;
        }

        .imgicon-purple {
            filter: brightness(0) invert(1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .po-header {
                flex-direction: column;
                text-align: center;
            }

            .po-title {
                font-size: 1.5rem;
            }

            .table-container {
                overflow-x: auto;
            }

            .table {
                min-width: 700px;
            }

            .pagination-container {
                flex-direction: column;
                text-align: center;
            }

            .home-button {
                bottom: 1rem;
                right: 1rem;
                padding: 0.75rem;
            }

            .home-button span {
                display: none;
            }
        }

        @media (max-width: 576px) {
            .page-container {
                padding: 1rem;
            }

            .po-container {
                padding: 1rem;
            }

            .table thead th,
            .table tbody td {
                padding: 0.5rem 0.25rem;
                font-size: 0.75rem;
            }
        }
    </style>
</head>

<body>
    <div class="page-container">
        <!-- Header -->
        <div class="header">
            <div class="right-logo">
                <div class="logo-line">
                    <img src="<?php echo e(asset('images/logo.png')); ?>" alt="Logo" />
                    <p class="slogan">PT. PUTERA WIBOWO BORNEO</p>
                </div>
                <div class="tagline">"Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"</div>
            </div>
        </div>

        <!-- Purchase Order Content -->
        <div class="po-container">
            <div class="po-header">
                <h1 class="po-title">TOTAL Pending PO Rp. 345.000.785</h1>
            </div>

            <!-- Search -->
            <div class="search-container">
                <form method="GET" action="<?php echo e(route('purchase-order.index')); ?>">
                    <input type="text" name="search" class="search-input" placeholder="Cari purchase order..." 
                           value="<?php echo e($search ?? ''); ?>">
                </form>
            </div>

            <!-- Table -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>NO</th>
                            <th>NO. MR</th>
                            <th>UNIT</th>
                            <th>STATUS</th>
                            <th>TANGGAL MR</th>
                            <th>TOTAL</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $purchaseOrders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $po): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td class="text-center"><?php echo e($po['no']); ?></td>
                            <td><?php echo e($po['no_mr']); ?></td>
                            <td><?php echo e($po['unit']); ?></td>
                            <td class="text-center">
                                <span class="status-badge <?php echo e($po['status'] == 'Ready WO' ? 'status-ready' : 'status-diajukan'); ?>">
                                    <?php echo e($po['status']); ?>

                                </span>
                            </td>
                            <td class="text-center"><?php echo e($po['tanggal_mr']); ?></td>
                            <td class="text-end"><?php echo e($po['total']); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <em>Tidak ada data purchase order yang ditemukan.</em>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <div class="pagination-info">
                    1-10 of <?php echo e($total ?? 0); ?>

                </div>
                <div class="pagination-controls">
                    <span>Rows per page: 10</span>
                    <a href="#" class="pagination-btn disabled">‹</a>
                    <span>1/10</span>
                    <a href="#" class="pagination-btn">›</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Home Button -->
    <a href="<?php echo e(route('home')); ?>" class="home-button">
        <img class="imgicon-purple" src="<?php echo e(asset('assets/icon/home.png')); ?>" alt="Home" width="40" height="40">
        <span>HOME</span>
    </a>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\pwbapp\resources\views/purchase-order.blade.php ENDPATH**/ ?>