<?php

namespace App\Http\Controllers\Invoice;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{
    /**
     * Display a listing of the invoices.
     */
    public function index(Request $request)
    {
        // Sample invoice data - in a real application, this would come from a database
        $invoices = collect([
            [
                'id' => 1,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 2,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 3,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 4,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 5,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 6,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 7,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ],
            [
                'id' => 8,
                'name' => '005/PWB-2025',
                'po' => '34034934',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Open',
                'tanggal_invoice' => '13 Juli 2025',
                'total' => 'Rp.34.660.000',
                'pembayaran' => 'Rp. 400.000',
                'sisa' => 'Rp. 600.000'
            ]
        ]);

        // Handle search functionality
        $search = $request->get('search');
        if ($search) {
            $invoices = $invoices->filter(function ($invoice) use ($search) {
                return stripos($invoice['name'], $search) !== false ||
                       stripos($invoice['po'], $search) !== false ||
                       stripos($invoice['unit'], $search) !== false ||
                       stripos($invoice['status'], $search) !== false;
            });
        }

        // Handle pagination
        $perPage = 10;
        $currentPage = $request->get('page', 1);
        $total = $invoices->count();
        $invoices = $invoices->forPage($currentPage, $perPage);

        return view('invoice', compact('invoices', 'total', 'currentPage', 'perPage', 'search'));
    }

    /**
     * Show the form for creating a new invoice.
     */
    public function create()
    {
        return view('invoice.create');
    }

    /**
     * Store a newly created invoice in storage.
     */
    public function store(Request $request)
    {
        // Implementation for storing invoice
        return redirect()->route('invoice.index')->with('success', 'Invoice created successfully.');
    }

    /**
     * Display the specified invoice.
     */
    public function show($id)
    {
        // Implementation for showing specific invoice
        return view('invoice.show', compact('id'));
    }

    /**
     * Show the form for editing the specified invoice.
     */
    public function edit($id)
    {
        // Implementation for editing invoice
        return view('invoice.edit', compact('id'));
    }

    /**
     * Update the specified invoice in storage.
     */
    public function update(Request $request, $id)
    {
        // Implementation for updating invoice
        return redirect()->route('invoice.index')->with('success', 'Invoice updated successfully.');
    }

    /**
     * Remove the specified invoice from storage.
     */
    public function destroy($id)
    {
        // Implementation for deleting invoice
        return redirect()->route('invoice.index')->with('success', 'Invoice deleted successfully.');
    }
}
