/* Layanan Page Specific Styles */

.layanan-container {
    padding: clamp(1rem, 3vw, 2rem);
    max-width: 100%;
    margin: 0 auto;
}

.layanan-title {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: bold;
    color: var(--purple);
    margin-bottom: clamp(1.5rem, 4vw, 2.5rem);
    text-align: left;
    margin-left: clamp(1rem, 3vw, 2rem);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: clamp(1rem, 3vw, 2rem);
    padding: 0 clamp(1rem, 3vw, 2rem);
    max-width: 100%;
}

.service-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: clamp(15px, 3vw, 25px);
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    height: fit-content;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.service-image {
    width: 100%;
    height: clamp(150px, 20vw, 200px);
    overflow: hidden;
    position: relative;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.service-content {
    padding: clamp(1rem, 3vw, 1.5rem);
}

.service-header {
    display: flex;
    align-items: center;
    margin-bottom: clamp(0.5rem, 2vw, 1rem);
    gap: clamp(0.5rem, 2vw, 1rem);
}

.service-icon {
    width: clamp(40px, 6vw, 60px);
    height: clamp(40px, 6vw, 60px);
    background: var(--purple);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.service-icon img {
    width: clamp(20px, 3vw, 30px);
    height: clamp(20px, 3vw, 30px);
    filter: brightness(0) invert(1);
}

.service-title {
    font-size: clamp(1.1rem, 3vw, 1.4rem);
    font-weight: bold;
    color: var(--purple);
    margin: 0;
    line-height: 1.2;
}

.service-subtitle {
    font-size: clamp(0.9rem, 2.5vw, 1.1rem);
    color: var(--text-primary);
    margin-bottom: clamp(0.8rem, 2vw, 1.2rem);
    font-weight: 500;
    line-height: 1.3;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-features li {
    font-size: clamp(0.8rem, 2vw, 0.95rem);
    color: var(--text-primary);
    margin-bottom: clamp(0.4rem, 1.5vw, 0.6rem);
    padding-left: clamp(1rem, 2.5vw, 1.5rem);
    position: relative;
    line-height: 1.4;
}

.service-features li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--purple);
    font-weight: bold;
    font-size: clamp(0.9rem, 2.2vw, 1.1rem);
}

/* Responsive Design */
@media (max-width: 768px) {
    .services-grid {
        grid-template-columns: 1fr;
        gap: clamp(1rem, 4vw, 1.5rem);
        padding: 0 clamp(0.5rem, 2vw, 1rem);
    }
    
    .layanan-title {
        text-align: center;
        margin-left: 0;
    }
    
    .service-header {
        flex-direction: column;
        text-align: center;
        gap: clamp(0.5rem, 2vw, 0.8rem);
    }
    
    .service-icon {
        margin: 0 auto;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: clamp(1.2rem, 3vw, 1.8rem);
    }
}

@media (min-width: 1025px) {
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: clamp(1.5rem, 3vw, 2rem);
        max-width: 1400px;
        margin: 0 auto;
    }
    
    .layanan-container {
        max-width: 1400px;
        margin: 0 auto;
    }
}

/* Color variations for different service cards */
.service-card:nth-child(1) .service-icon {
    background: linear-gradient(135deg, #4a90e2, #357abd);
}

.service-card:nth-child(2) .service-icon {
    background: linear-gradient(135deg, #f39c12, #e67e22);
}

.service-card:nth-child(3) .service-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

/* Enhanced card styling for better visual hierarchy */
.service-card:nth-child(1) {
    border-left: 4px solid #4a90e2;
}

.service-card:nth-child(2) {
    border-left: 4px solid #f39c12;
}

.service-card:nth-child(3) {
    border-left: 4px solid #e74c3c;
}

/* Ensure proper spacing and prevent overflow */
.layanan-container {
    overflow-x: hidden;
}

.service-card {
    max-width: 100%;
    word-wrap: break-word;
}

.service-features li {
    word-wrap: break-word;
    overflow-wrap: break-word;
}
