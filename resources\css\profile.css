/* --- Global Styles & Variables --- */

.container {
    width: 100%;
    max-width: 100%;
    /* background-color: rgba(255, 255, 255, 0.4); */
    /* box-shadow: 0 10px 30px rgba(0, 0, 0, 0.07); */
    /* backdrop-filter: blur(10px); */
    /* -webkit-backdrop-filter: blur(10px); */
    /* border: 1px solid rgba(255, 255, 255, 0.5); */
}

/* --- Header --- */
.main-header {
    display: flex;
    align-items: center;
    gap: 0.1rem;
    margin-bottom: 0.5rem;
}

.logo img {
    width: 160px;
    height: auto;
}

.company-title h1 {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--brand-purple);
    line-height: 1.2;
    margin: 0;
}

.tagline {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0.5rem 0 0;
}

/* --- Content Grid Layout --- */
.content-grid {
    gap: 2rem;
}

.left-column, .right-column {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.about-us p {
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
}

/* --- General Card Styling --- */


.card h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--brand-purple);
    text-align: center;
    margin-top: 0;
    margin-bottom: 1.5rem;
}

.vision-card p {
    text-align: center;
    font-style: italic;
    line-height: 1.7;
    margin: 0;
}

/* --- Partners Card --- */
.partners-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.partner-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.partner-item img {
    height: 40px;
    width: 40px;
    object-fit: contain;
}

.partner-info {
    display: flex;
    flex-direction: column;
}
.partner-info p{
    font-size: 0.7rem;
    margin: 0;
}
.join-us .question-mark {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    background-color: #d1d1d1;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
}

/* --- Mission Card --- */
.mission-card ul {
    /* list-style: none; */
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mission-card li {
    display: flex;
    align-items: flex-start;
    font-size: 0.9rem;
    /* gap: 0.75rem; */
    line-height: 1.4;
}

.mission-card li::before {
    content: '✔';
    display: inline-block;
    width: 22px;
    height: 22px;
    flex-shrink: 0;
    border-radius: 50%;
    background-color: var(--brand-purple);
    color: rgb(25, 36, 99);
    font-size: 14px;
    text-align: center;
    line-height: 23px;
    margin-top: 2px;
}

/* --- Footer --- */
.main-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    margin-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-item .icon {
    width: 40px;
    height: 40px;
}

.contact-item p {
    margin: 0;
    line-height: 1.4;
    font-size: 0.9rem;
}

.contact-item p strong {
    font-weight: 600;
}


/* --- Responsive Design --- */
@media (max-width: 1024px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    body {
        padding: 1rem;
    }
    .container {
        padding: 0;
    }
    .main-header {
        flex-direction: column;
        text-align: center;
        gap: 0.2rem;
    }
    .main-footer {
        flex-direction: column;
        align-items: flex-start;
    }
    .partners-grid {
        grid-template-columns: 1fr;
    }
    .home-button {
        bottom: 1rem;
        right: 1rem;
        width: 60px;
        height: 60px;
    }
    .home-button svg {
        width: 24px;
        height: 24px;
    }
}