<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'src' => '',
    'alt' => '',
    'class' => '',
    'width' => null,
    'height' => null,
    'loading' => 'lazy',
    'skeleton' => false,
    'placeholder' => null,
    'srcset' => null,
    'sizes' => null,
    'priority' => false,
    'fallback' => null
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'src' => '',
    'alt' => '',
    'class' => '',
    'width' => null,
    'height' => null,
    'loading' => 'lazy',
    'skeleton' => false,
    'placeholder' => null,
    'srcset' => null,
    'sizes' => null,
    'priority' => false,
    'fallback' => null
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // Determine if this image should be lazy loaded
    $shouldLazyLoad = !$priority && $loading === 'lazy';
    
    // Build classes
    $classes = collect(['lazy-image'])
        ->when($skeleton, fn($collection) => $collection->push('skeleton-loading'))
        ->when($class, fn($collection) => $collection->push($class))
        ->implode(' ');
    
    // Prepare attributes
    $attributes = collect([
        'alt' => $alt,
        'class' => $classes,
        'loading' => $priority ? 'eager' : $loading
    ])
    ->when($width, fn($collection) => $collection->put('width', $width))
    ->when($height, fn($collection) => $collection->put('height', $height))
    ->when($sizes, fn($collection) => $collection->put('sizes', $sizes));
    
    // Handle lazy loading attributes
    if ($shouldLazyLoad) {
        $attributes->put('data-lazy-src', $src);
        if ($srcset) {
            $attributes->put('data-lazy-srcset', $srcset);
        }
        if ($skeleton) {
            $attributes->put('data-skeleton', 'true');
        }
        
        // Set placeholder as initial src
        $initialSrc = $placeholder ?: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+';
        $attributes->put('src', $initialSrc);
    } else {
        // For priority/eager loading images
        $attributes->put('src', $src);
        if ($srcset) {
            $attributes->put('srcset', $srcset);
        }
    }
?>

<img <?php echo e($attributes->map(fn($value, $key) => $key . '="' . e($value) . '"')->implode(' ')); ?>

     <?php if($fallback): ?>
         onerror="this.onerror=null; this.src='<?php echo e($fallback); ?>';"
     <?php endif; ?>
/>
<?php /**PATH C:\xampp\htdocs\pwbapp\resources\views/components/lazy-image.blade.php ENDPATH**/ ?>