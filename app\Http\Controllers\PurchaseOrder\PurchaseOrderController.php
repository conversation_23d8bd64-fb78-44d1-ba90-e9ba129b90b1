<?php

namespace App\Http\Controllers\PurchaseOrder;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PurchaseOrderController extends Controller
{
    /**
     * Display a listing of the purchase orders.
     */
    public function index(Request $request)
    {
        // Sample purchase order data - in a real application, this would come from a database
        $purchaseOrders = collect([
            [
                'id' => 1,
                'no' => 1,
                'no_mr' => 'MR : 001/JK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Ready WO',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 2,
                'no' => 2,
                'no_mr' => 'MR : 001/JK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Ready WO',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 3,
                'no' => 3,
                'no_mr' => 'MR : 001/JK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 4,
                'no' => 4,
                'no_mr' => 'MR : 001/JK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 5,
                'no' => 5,
                'no_mr' => 'MR : 001/JK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 6,
                'no' => 6,
                'no_mr' => 'MR : 001/JK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 7,
                'no' => 7,
                'no_mr' => 'MR : 001/JK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ],
            [
                'id' => 8,
                'no' => 8,
                'no_mr' => 'MR : 001/JK-IV/2025',
                'unit' => 'HD78456 KOMATSU',
                'status' => 'Diajukan',
                'tanggal_mr' => '13 Juli 2025',
                'total' => 'Rp.34.660.000'
            ]
        ]);

        // Handle search functionality
        $search = $request->get('search');
        if ($search) {
            $purchaseOrders = $purchaseOrders->filter(function ($po) use ($search) {
                return stripos($po['no_mr'], $search) !== false ||
                       stripos($po['unit'], $search) !== false ||
                       stripos($po['status'], $search) !== false;
            });
        }

        // Handle pagination
        $perPage = 10;
        $currentPage = $request->get('page', 1);
        $total = $purchaseOrders->count();
        $purchaseOrders = $purchaseOrders->forPage($currentPage, $perPage);

        return view('purchase-order', compact('purchaseOrders', 'total', 'currentPage', 'perPage', 'search'));
    }

    /**
     * Show the form for creating a new purchase order.
     */
    public function create()
    {
        return view('purchase-order.create');
    }

    /**
     * Store a newly created purchase order in storage.
     */
    public function store(Request $request)
    {
        // Implementation for storing purchase order
        return redirect()->route('purchase-order.index')->with('success', 'Purchase Order created successfully.');
    }

    /**
     * Display the specified purchase order.
     */
    public function show($id)
    {
        // Implementation for showing specific purchase order
        return view('purchase-order.show', compact('id'));
    }

    /**
     * Show the form for editing the specified purchase order.
     */
    public function edit($id)
    {
        // Implementation for editing purchase order
        return view('purchase-order.edit', compact('id'));
    }

    /**
     * Update the specified purchase order in storage.
     */
    public function update(Request $request, $id)
    {
        // Implementation for updating purchase order
        return redirect()->route('purchase-order.index')->with('success', 'Purchase Order updated successfully.');
    }

    /**
     * Remove the specified purchase order from storage.
     */
    public function destroy($id)
    {
        // Implementation for deleting purchase order
        return redirect()->route('purchase-order.index')->with('success', 'Purchase Order deleted successfully.');
    }
}
